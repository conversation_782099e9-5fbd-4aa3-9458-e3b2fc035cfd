{"name": "gpayo-callbacks-kafka-core", "version": "1.0.0", "description": "", "private": true, "type": "commonjs", "engines": {"node": ">=20"}, "main": "index.js", "scripts": {"start": "node --watch index.js", "lint": "eslint **/*.js --fix", "format": "prettier **/*.js --write", "prepare": "husky", "test": "jest --config jest.config.js"}, "keywords": [], "author": "Globe Telecom", "license": "ISC", "devDependencies": {"@eslint/js": "9.9.0", "eslint": "9.9.0", "eslint-config-prettier": "9.1.0", "globals": "15.9.0", "husky": "9.1.5", "jest": "29.7.0", "jest-sonar": "0.2.16", "lint-staged": "15.2.10", "module-alias-jest": "0.0.3", "prettier": "3.3.3"}, "dependencies": {"@aws-sdk/client-dynamodb": "3.804.0", "@aws-sdk/lib-dynamodb": "3.804.0", "@aws-sdk/util-dynamodb": "3.804.0", "@kafkajs/confluent-schema-registry": "3.8.0", "axios": "1.7.7", "axios-logger": "2.8.1", "axios-retry": "4.5.0", "env-schema": "6.0.0", "module-alias": "2.2.3", "node-rdkafka": "3.3.1", "pino": "9.3.2", "pino-pretty": "11.2.2", "uuid": "11.1.0"}, "_moduleAliases": {"@configs": "src/configurations", "@events": "src/events", "@utils": "src/utils", "@repositories": "src/repositories", "@services": "src/services", "@exception": "src/configurations/exception", "@root": "."}, "lint-staged": {"**/*.js": ["eslint --cache --fix --max-warnings 0 .", "prettier --write", "jest --bail --findRelatedTests"]}}