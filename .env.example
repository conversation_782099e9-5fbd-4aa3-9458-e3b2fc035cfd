NODE_ENV=local
LOG_LEVEL=debug

#----------------------------------------------------#
#----------------- KAFKA CONFIGURATION --------------#
#----------------------------------------------------#
# Broker connection
KAFKA_BROKERS=localhost:19092
KAFKA_TOPICS=topic_name
KAFKA_CONSUMER_GROUP=consumer_group_name

# Authentication
KAFKA_SASL_USERNAME=username
KAFKA_SASL_PASSWORD=password
KAFKA_SECURITY_PROTOCOL=SASL_SSL
KAFKA_MECHANISM=PLAIN
KAFKA_ENDPOINT_IDENTIFICATION_ALGORITHM=https

# CONSUMER CONFIGURATION
KAFKA_AUTO_OFFSET_RESET=earliest
KAFKA_DEBUG_SCOPE=consumer
KAFKA_CONSUMER_READ_NUMBER_OF_MESSAGES_PER_INTERVAL=20 # no. of messages to consume per poll/read
KAFKA_CONSUMER_READ_INTERVAL_MS=1000 # poll/read interval in milliseconds
KAFKA_CONSUMER_LOCAL_QUEUE_MAX_MESSAGES_KBYTES=512 # max messages in kbytes
KAFKA_CONSUMER_LOCAL_QUEUE_MIN_MESSAGES=100 # min messages in queue
KAFKA_CONSUMER_REQUEST_WAIT_MAX_MS=5000 # max wait time for request in milliseconds
KAFKA_CONSUMER_REQUEST_ERROR_BACKOFF_MS=500 # backoff time for request in milliseconds
KAFKA_CONSUMER_BATCH_MAX_MESSAGE_BYTES=4096 # max message bytes in batch

# PRODUCER CONFIGURATION
KAFKA_PRODUCE_BROKERS=localhost:19092
KAFKA_PRODUCER_SASL_PASSWORD=CHANGE_ME
KAFKA_PRODUCER_SASL_USERNAME=CHANGE_ME

KAFKA_PRODUCER_QUEUE_BUFFERING_MAX_KBYTES=16384
KAFKA_PRODUCER_LINGER_MS=10
KAFKA_PRODUCER_MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION=2
KAFKA_PRODUCER_RETRIES=3
KAFKA_PRODUCER_BATCH_NUM_MESSAGES=5
KAFKA_PRODUCER_ACKS=-1
TOOGLE_GCASH=true
TOOGLE_CTL=true

KAFKA_TOPIC_POSTING=topic_name
KAFKA_PRODUCER_SCHEME=1
KAFKA_MESSAGE_EVENT_POSTING=event_name


#----------------------------------------------------#
#--------------- KAFKA SCHEMA REGISTRY --------------#
#----------------------------------------------------#
KAFKA_SCHEMA_REGISTRY_TOGGLE=false
KAFKA_SCHEMA_REGISTRY_HOST=http://localhost:18081
KAFKA_SCHEMA_REGISTRY_USERNAME=username
KAFKA_SCHEMA_REGISTRY_PASSWORD=password
KAFKA_SCHEMA_REGISTRY_SUFFIX=value

#----------------------------------------------------#
#--------------- XENDIT CONFIGURATION ---------------#
#----------------------------------------------------#
XENDIT_HOST=https://api.xendit.co
XENDIT_SECRET_KEY=CHANGE_ME
XENDIT_CREATE_PAYMENT_CODE_ENDPOINT=/payment_codes

#----------------------------------------------------#
#----------- CHANNEL CB CONFIGURATION ---------------#
#----------------------------------------------------#
CXS_CALLBACK_API_KEY=api_key

#----------------------------------------------------#
#-------------- DYNAMODB CONFIGURATION --------------#
#----------------------------------------------------#
DDB_AWS_REGION=ap-southeast-1
DDB_ENDPOINT=http://localhost:4566
DDB_MAX_ATTEMPTS=3
DDB_TTL_DAYS=365

#----------------------------------------------------#
#-------------------- TABLE NAMES -------------------#
#----------------------------------------------------#
DB_TABLE_TRANSACTION="isg-gpayo-local-transLogs-ns"
DB_TABLE_CHANNEL="isg-gpayo-local-channel-ns"
DB_TABLE_EVENT_LOGS="isg-gpayo-local-eventLogs-ns"

#----------------------------------------------------#
#------------------ HTTP CONFIGS --------------------#
#----------------------------------------------------#
SKIP_SSL_VERIFICATION=false