const dynamodb = require('@configs/dynamodb');
const {
  QueryCommand,
  PutCommand,
  UpdateCommand,
  DeleteCommand,
  ScanCommand,
  GetCommand,
  TransactWriteCommand
} = require('@aws-sdk/lib-dynamodb');
const { transformJsonAttributes, getTTL } = require('@utils/database');

class BaseRepository {
  constructor(tableName) {
    this.tableName = tableName;
    this.ddbClient = dynamodb;
  }
  async scan(params = {}) {
    return this.ddbClient.send(
      new ScanCommand({
        TableName: this.tableName,
        ...params
      })
    );
  }

  async save(params = {}) {
    return this.ddbClient.send(
      new PutCommand({
        TableName: this.tableName,
        ...params
      })
    );
  }

  async query(params = {}) {
    return this.ddbClient.send(
      new QueryCommand({
        TableName: this.tableName,
        ...params
      })
    );
  }

  async get(params = {}) {
    return this.ddbClient.send(
      new GetCommand({
        TableName: this.tableName,
        ...params
      })
    );
  }

  async update(params = {}) {
    return this.ddbClient.send(
      new UpdateCommand({
        TableName: this.tableName,
        ...params
      })
    );
  }

  async delete(params = {}) {
    return this.ddbClient.send(
      new DeleteCommand({
        TableName: this.tableName,
        ...params
      })
    );
  }

  async transactional(queries = []) {
    return this.ddbClient.send(
      new TransactWriteCommand({
        TransactItems: queries
      })
    );
  }

  buildCreateParams(data) {
    return {
      TableName: this.tableName,
      Item: {
        data,
        ttl: getTTL()
      }
    };
  }

  buildUpdateParams(key, data) {
    return {
      TableName: this.tableName,
      Key: key,
      ...transformJsonAttributes(data)
    };
  }
}

module.exports = BaseRepository;
